// 声明所有全局变量
#ifndef GLOBALS_H
#define GLOBALS_H
#include "main.h"
#include "rtc.h"

extern 	char str[20];
extern	uint8_t rx1_tmp[10];
extern	uint8_t tx1_tmp[10];
extern	uint8_t rx2_tmp[10];
extern	uint8_t tx2_tmp[10];
extern  uint8_t rx3_tmp[10];
extern	uint8_t tx3_tmp[10];
extern	uint8_t data;
extern	uint32_t ADC_Value[60];  // 3通道 × 20采样 = 60个数据
extern  int ad1,ad2,ad3,ad4,ad5,tp,td,button;
extern  float pw;
extern  float mcu_temp;  // MCU内部温度

extern float pitch ;  // 俯仰角
extern float roll ;    // 横滚角
extern float yaw ;      // 方位角

// GSM模块相关全局变量
extern char gsm_ccid[32];        // GSM模块CCID号，设备启动时获取一次
extern int8_t gsm_signal_quality; // GSM信号质量 (-128表示无效，0-31表示信号强度)

// ZL自定义指令功能相关全局变量
extern uint32_t pack_count_threshold;  // N指令：存储N条数据后再启动GSM模块
extern uint8_t pack_count_enabled;     // N指令是否启用标志
extern uint32_t speed_threshold_kmh;   // A指令：速度阈值（节 knots）
extern uint32_t fast_send_sleep_time;  // B指令：快发模式休眠时间（秒）
extern uint8_t speed_threshold_enabled; // A指令是否启用标志

// 长时间休眠跟踪机制
extern uint32_t target_sleep_seconds;   // 目标休眠总时间（秒）
extern uint32_t elapsed_sleep_seconds;  // 已休眠时间（秒）
extern uint8_t long_sleep_active;       // 长时间休眠是否激活

// 休眠时间管理（分离智能休眠和用户设置）
extern uint32_t user_sleep_seconds;     // 用户设置的休眠时间（S/M/H/D指令）
extern uint32_t smart_sleep_seconds;    // 智能休眠时间（工作时间段外使用）
extern uint8_t smart_sleep_active;      // 智能休眠是否激活

// 工作时间段结构体
typedef struct {
    uint8_t startHour;
    uint8_t startMinute;
    uint8_t endHour;
    uint8_t endMinute;
    uint8_t enabled;  // 0: 禁用, 1: 启用
} WorkTimeSlot;

// 全局变量
extern unsigned int sleepSeconds; // 休眠时间

// 设备唤醒次数计数器
extern uint32_t device_wakeup_count;

// T3/T0指令相关全局变量
extern uint8_t gps_enable_mode;  // GPS启动模式：0-默认不启动，1-启动GPS

#endif // GLOBALS_H
