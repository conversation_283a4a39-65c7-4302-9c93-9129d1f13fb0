/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "gpio.h"
#include "rtc.h"
#include "adc.h"
#include "usart.h"
#include "dma.h"
#include "spi.h"
#include "i2c.h"
#include "stm32l0xx_hal_pwr.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GPS.h"
#include "rtc_sync.h"
#include "rtc_config.h"
#include "system_modules.h"
#include "GSM.h"
#include "network_command.h"
#include "globals.h"

// External variables
extern uint8_t uart1_rx_buffer[];
extern uint8_t lpuart1_rx_buffer[];
extern char gps_buffer[];
extern uint16_t gps_buffer_index;
extern char gsm_ccid[];
extern int8_t gsm_signal_quality;
extern uint8_t gsm_zl_command_received;

#define GPS_BUFFER_SIZE 1024  // 增加缓冲区大小，避免溢出
#define NETWORK_CMD_BUFFER_SIZE 32

extern GPS_Data_t gps_data;
extern uint8_t gps_new_data;
extern uint8_t gps_data_ready;
extern uint32_t ADC_Value[];
extern float pw;

// Function declarations
extern HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
extern void GPS_ParseData(void);
extern uint8_t GPS_CheckQualityTimeout(uint32_t timeout_ms);
extern void GPS_UseTempDataIfNeeded(void);
extern GPS_TempData_t gps_temp_data;

/* ADC相关宏定义 */
#define ADC_SAMPLE_COUNT            20       // ADC采样次数
#define ADC_BUFFER_SIZE             60       // ADC缓冲区大小 (3通道 × 20采样)

/* VREFINT校准相关宏定义 */
#define VREFINT_CAL_VREF            3000     // 校准时的参考电压(mV)
#define VREFINT_CAL_ADDR            ((uint16_t*)0x1FF80078)  // VREFINT校准地址
#define VREFINT_CAL_VALUE           (*VREFINT_CAL_ADDR)      // VREFINT校准值

/* RTC时钟修正相关全局变量 */
uint32_t last_actual_logical_seconds = 0;  // 保存最后一次实际执行的逻辑时间

/**
 * @brief  应用RTC时钟修正
 * @param  logical_seconds: 请求的逻辑休眠时间（秒）
 * @param  actual_logical_seconds: 输出实际能执行的逻辑时间（秒）
 * @retval 返回RTC应该设置的物理时间（秒）
 */
/* 应用RTC时钟修正，将逻辑时间转换为物理时间 */
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds)
{
    uint32_t max_logical_time = RTC_MAX_LOGICAL_SLEEP_SECONDS;
    uint32_t actual_logical_time;
    uint32_t physical_rtc_time;

    // 运行时验证最大逻辑时间设置是否正确
    static uint8_t verified = 0;
    if (!verified) {
        uint32_t test_physical = (uint32_t)(RTC_MAX_LOGICAL_SLEEP_SECONDS * RTC_CLOCK_CORRECTION_FACTOR);
        if (test_physical > RTC_MAX_SLEEP_SECONDS_RAW) {
            printf("WARNING: RTC_MAX_LOGICAL_SLEEP_SECONDS too large! %lu -> %lu > %u\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        } else {
            printf("RTC LSE clock config verified: %lu -> %lu <= %u (no correction needed)\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        }
        verified = 1;
    }

    // 确定实际能执行的逻辑时间
    if (logical_seconds <= max_logical_time) {
        actual_logical_time = logical_seconds;
    } else {
        actual_logical_time = max_logical_time;
        printf("RTC correction limited logical time from %lu to %lu seconds\r\n",
               logical_seconds, actual_logical_time);
    }

    // 计算RTC应该设置的物理时间
    physical_rtc_time = (uint32_t)(actual_logical_time * RTC_CLOCK_CORRECTION_FACTOR);

    // 安全检查，防止溢出
    if (physical_rtc_time > RTC_MAX_SLEEP_SECONDS_RAW) {
        physical_rtc_time = RTC_MAX_SLEEP_SECONDS_RAW;
        printf("RTC physical time limited to maximum: %lu seconds\r\n", physical_rtc_time);
    }

    // 输出实际能执行的逻辑时间
    if (actual_logical_seconds != NULL) {
        *actual_logical_seconds = actual_logical_time;
    }

    // 调试输出
    if (logical_seconds != actual_logical_time) {
        printf("RTC LSE: requested=%lu, actual_logical=%lu, physical=%lu seconds\r\n",
               logical_seconds, actual_logical_time, physical_rtc_time);
    } else {
        printf("RTC LSE: logical=%lu -> physical=%lu seconds (1:1 mapping)\r\n",
               actual_logical_time, physical_rtc_time);
    }

    return physical_rtc_time;
}

// STM32内部温度传感器计算函数
float Calculate_MCU_Temperature(uint32_t temp_adc_value);

// 模块化函数声明
// 电源管理模块
HAL_StatusTypeDef PowerModule_Init(void);
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void);
void PowerModule_EnablePeripherals(void);
void PowerModule_DisablePeripherals(void);

// GPS模块
HAL_StatusTypeDef GPSModule_Init(void);
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t unused_timeout, uint8_t is_first_boot);
void GPSModule_PowerOn(void);
void GPSModule_PowerOff(void);
void GPSModule_ClearData(void);
HAL_StatusTypeDef GPSModule_SyncRTC(void);

// 传感器模块
HAL_StatusTypeDef SensorModule_Init(void);
HAL_StatusTypeDef SensorModule_ReadData(void);

// 数据处理模块
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size);
void DataModule_PrintData(const char *data_string);

// 系统控制模块
void SystemModule_PrintCurrentTime(void);
void SystemModule_EnterSleepMode(void);

// 电池保护模块
uint8_t BatteryProtection_CheckVoltage(void);
void BatteryProtection_EnterLowVoltageMode(void);
void BatteryProtection_ExitLowVoltageMode(void);

// 时间管理函数
uint32_t GetCurrentWakeupTime(void);

// 全局变量：保存设备唤醒后的RTC时间
RTC_TimeTypeDef current_wakeup_time;
RTC_DateTypeDef current_wakeup_date;
uint8_t rtc_time_valid = 0;  // RTC时间有效标志：0=无效(默认时间), 1=有效(已同步)

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// 休眠配置
#define SLEEP_DURATION_SECONDS 30       // 休眠时间设置
#define UART1_RX_BUFFER_SIZE 256        // UART接收缓冲区大小

// 历史数据发送配置
#define HISTORICAL_DATA_SEND_LIMIT 100    // 每次发送历史数据的条数限制
#define HISTORICAL_DATA_BATCH_SIZE 2      // 批量发送最大条数 最大8条
#define BATCH_SEPARATOR "+M+"             // 数据分隔符
#define BATCH_BUFFER_SIZE 300            // 批量数据缓冲区大小 根据发送条数调整

// 静态全局缓冲区，避免栈溢出
static char g_batch_data_buffer[BATCH_BUFFER_SIZE];
static uint8_t g_read_data_buffer[SPI_FLASH_RECORD_SIZE];

// 电池电压保护阈值
#define BATTERY_LOW_VOLTAGE_THRESHOLD    3.4f    // 低电压保护阈值（V）
#define BATTERY_RECOVERY_VOLTAGE_THRESHOLD 3.6f  // 电压恢复阈值（V）
#define LOW_VOLTAGE_SLEEP_INTERVAL_SECONDS  3600  // 低电压模式休眠间隔（秒），默认1小时
/**
 * @brief 批量读取历史数据并拼接（使用全局缓冲区优化）
 * @param batch_buffer 拼接后的数据缓冲区
 * @param buffer_size 缓冲区大小
 * @param actual_count 实际读取的记录数
 * @return HAL_StatusTypeDef 执行状态
 */
/* 批量读取历史数据并拼接成字符串 */
HAL_StatusTypeDef BatchHistoricalData(char* batch_buffer, uint32_t buffer_size, uint32_t* actual_count)
{
    if (batch_buffer == NULL || actual_count == NULL || buffer_size == 0) {
        return HAL_ERROR;
    }

    uint32_t count = 0;
    uint32_t total_length = 0;
    memset(batch_buffer, 0, buffer_size);
    *actual_count = 0;
    while (count < HISTORICAL_DATA_BATCH_SIZE && SPI_FLASH_GetRecordCount() > 0) {
        uint16_t data_size = SPI_FLASH_RECORD_SIZE;
        if (SPI_FLASH_ReadRecordEx(0, g_read_data_buffer, &data_size, 0) == HAL_OK) {
            g_read_data_buffer[SPI_FLASH_RECORD_SIZE - 1] = '\0';
            if (strlen((char*)g_read_data_buffer) > 0 && g_read_data_buffer[0] != 0xFF) {
                uint32_t data_len = strlen((char*)g_read_data_buffer);
                uint32_t separator_len = (count > 0) ? strlen(BATCH_SEPARATOR) : 0;
                if (total_length + data_len + separator_len >= buffer_size - 1) {
                    printf("Batch buffer full, stopping at %lu records\r\n", count);
                    break;
                }

                if (count > 0) {
                    strcat(batch_buffer, BATCH_SEPARATOR);
                    total_length += separator_len;
                }

                strcat(batch_buffer, (char*)g_read_data_buffer);
                total_length += data_len;
                count++;
                printf("Batched record %lu, length: %lu\r\n", count, data_len);
                SPI_FLASH_MarkMultipleAsRead(1);
            }

else {
                printf("Invalid data found, marking as read and skipping\r\n");
                SPI_FLASH_MarkMultipleAsRead(1);
            }

        }

else {
            printf("Failed to read historical data\r\n");
            break;
        }

    }

    *actual_count = count;
    if (count > 0) {
        printf("Batch completed: %lu records, total length: %lu bytes\r\n", count, total_length);
        return HAL_OK;
    }

else {
        return HAL_ERROR;
    }

}

// 电压监测测试配置
#define ENABLE_VOLTAGE_MONITOR_TEST 0   // 1=启用电压监测测试, 0=禁用
#define VOLTAGE_MONITOR_DURATION 60     // 电压监测持续时间（秒）

#define GPS_TIMEOUT_NORMAL 120        // GPS常规定位等待时间（秒）
#define GPS_TIMEOUT_FIRST_BOOT 300    // GPS初次启动等待时间（秒）

// GPS调试信息控制宏（0=禁用，1=启用）
#define GPS_DEBUG_ENABLE 0

// AB指令测试宏（0=使用真实GPS速度，1=使用虚拟速度）
#define AB_COMMAND_TEST_ENABLE 0

// 虚拟速度值（用于AB指令测试，单位：节 knots）
#define VIRTUAL_SPEED_VALUE 15.0f  // 可以修改这个值来测试不同速度

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
volatile uint8_t rtcWakeupFlag = 0;
extern uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE];
extern LSM6DS3_Data imuData;
extern LSM6DS3_Attitude attitude;
uint32_t wakeup_counter = 0;
uint8_t is_first_boot = 1;
uint8_t low_voltage_protection_active = 0;
uint8_t low_voltage_mode_active = 0;  // 低电压模式激活标志（3.4V-3.6V之间）
volatile uint8_t gps_task_completed = 0;
volatile uint8_t gsm_can_start = 0;
osMessageQId gpsDataQueueHandle;
osMessageQId sensorDataQueueHandle;
osSemaphoreId gpsReadySemHandle;
osSemaphoreId sensorReadySemHandle;
osSemaphoreId dataSentSemHandle;
osSemaphoreId gsmReadySemHandle;
osSemaphoreId gpsStartSemHandle;
osSemaphoreId sensorStartSemHandle;
osSemaphoreId gsmStartSemHandle;
#define GPS_READY_BIT       (1UL << 0)
#define SENSOR_READY_BIT    (1UL << 1)
#define DATA_SENT_BIT       (1UL << 2)
#define SLEEP_READY_BIT     (1UL << 3)
/* USER CODE END Variables */
osThreadId GPSTaskHandle;
uint32_t GPSTaskBuffer[ 512 ];
osStaticThreadDef_t GPSTaskControlBlock;
osThreadId AccelTaskHandle;
uint32_t AccelTaskBuffer[ 512 ];
osStaticThreadDef_t AccelTaskControlBlock;
osThreadId GSMTaskHandle;
uint32_t GSMTaskBuffer[ 512 ];
osStaticThreadDef_t GSMTaskControlBlock;
osThreadId FlashTaskHandle;
uint32_t FlashTaskBuffer[ 128 ];
osStaticThreadDef_t FlashTaskControlBlock;
osThreadId myPowerTaskHandle;
uint32_t myPowerTaskBuffer[ 512 ];
osStaticThreadDef_t myPowerTaskControlBlock;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void EnterStopMode(void);
void BlinkLEDs(uint8_t count, uint32_t delay);
void SystemClock_Config(void);
/* USER CODE END FunctionPrototypes */

void StartGPSTask(void const * argument);
void StartAccelTask(void const * argument);
void StartGSMTask(void const * argument);
void StartFlashTask(void const * argument);
void StartPowerTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];
/* 获取空闲任务内存配置 */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;

 /* place for user code */
}

/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
/* FreeRTOS系统初始化，创建任务、信号量和队列 */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */

 /* add mutexes, ... */

  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  osSemaphoreDef(gpsReadySem);
  gpsReadySemHandle = osSemaphoreCreate(osSemaphore(gpsReadySem), 1);
  osSemaphoreDef(sensorReadySem);
  sensorReadySemHandle = osSemaphoreCreate(osSemaphore(sensorReadySem), 1);
  osSemaphoreDef(dataSentSem);
  dataSentSemHandle = osSemaphoreCreate(osSemaphore(dataSentSem), 1);
  osSemaphoreDef(gsmReadySem);
  gsmReadySemHandle = osSemaphoreCreate(osSemaphore(gsmReadySem), 1);
  osSemaphoreDef(gpsStartSem);
  gpsStartSemHandle = osSemaphoreCreate(osSemaphore(gpsStartSem), 1);
  osSemaphoreDef(sensorStartSem);
  sensorStartSemHandle = osSemaphoreCreate(osSemaphore(sensorStartSem), 1);
  osSemaphoreDef(gsmStartSem);
  gsmStartSemHandle = osSemaphoreCreate(osSemaphore(gsmStartSem), 1);
  osSemaphoreWait(gpsReadySemHandle, 0);
  osSemaphoreWait(sensorReadySemHandle, 0);
  osSemaphoreWait(dataSentSemHandle, 0);
  osSemaphoreWait(gsmReadySemHandle, 0);
  osSemaphoreWait(gpsStartSemHandle, 0);
  osSemaphoreWait(sensorStartSemHandle, 0);
  osSemaphoreWait(gsmStartSemHandle, 0);

  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */

 /* start timers, add new ones, ... */

  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  osMessageQDef(gpsDataQueue, 2, GPSQueueData_t);
  gpsDataQueueHandle = osMessageCreate(osMessageQ(gpsDataQueue), NULL);
  osMessageQDef(sensorDataQueue, 2, SensorQueueData_t);
  sensorDataQueueHandle = osMessageCreate(osMessageQ(sensorDataQueue), NULL);

  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of GPSTask */
  osThreadStaticDef(GPSTask, StartGPSTask, osPriorityNormal, 0, 512, GPSTaskBuffer, &GPSTaskControlBlock);
  GPSTaskHandle = osThreadCreate(osThread(GPSTask), NULL);

  /* definition and creation of AccelTask */
  osThreadStaticDef(AccelTask, StartAccelTask, osPriorityIdle, 0, 512, AccelTaskBuffer, &AccelTaskControlBlock);
  AccelTaskHandle = osThreadCreate(osThread(AccelTask), NULL);

  /* definition and creation of GSMTask */
  osThreadStaticDef(GSMTask, StartGSMTask, osPriorityIdle, 0, 512, GSMTaskBuffer, &GSMTaskControlBlock);
  GSMTaskHandle = osThreadCreate(osThread(GSMTask), NULL);

  /* definition and creation of FlashTask */
  osThreadStaticDef(FlashTask, StartFlashTask, osPriorityIdle, 0, 128, FlashTaskBuffer, &FlashTaskControlBlock);
  FlashTaskHandle = osThreadCreate(osThread(FlashTask), NULL);

  /* definition and creation of myPowerTask */
  osThreadStaticDef(myPowerTask, StartPowerTask, osPriorityIdle, 0, 512, myPowerTaskBuffer, &myPowerTaskControlBlock);
  myPowerTaskHandle = osThreadCreate(osThread(myPowerTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  printf("RTOS OK\r\n");

  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartGPSTask */
/**
  * @brief  GPS任务实现
  * @param  argument: 不使用
  * @retval None
  */
/* USER CODE END Header_StartGPSTask */
/* GPS任务主函数，负责GPS数据采集和RTC同步 */
void StartGPSTask(void const * argument)
{
  /* USER CODE BEGIN StartGPSTask */

 /* 无限循环 */
  for(;;)
  {
    if (osSemaphoreWait(gpsStartSemHandle, osWaitForever) == osOK) {
      GPSModule_PowerOn();
      memset(&gps_data, 0, sizeof(GPS_Data_t));
      printf("GPS data before collection: valid=%d, time=%02d:%02d:%02d\r\n",
             gps_data.valid, gps_data.hour, gps_data.minute, gps_data.second);
      HAL_StatusTypeDef gps_result = GPSModule_WaitForData(0, is_first_boot);
      printf("GPS data after collection: result=%d, valid=%d, time=%02d:%02d:%02d\r\n",
             gps_result, gps_data.valid, gps_data.hour, gps_data.minute, gps_data.second);
      static uint32_t last_gps_time_seconds = 0;
      uint32_t current_gps_time_seconds = gps_data.hour * 3600 + gps_data.minute * 60 + gps_data.second;
      if (gps_data.valid && current_gps_time_seconds == last_gps_time_seconds) {
          printf("WARNING: GPS time unchanged, possible frozen data! Invalidating...\r\n");
          gps_data.valid = 0;
      }

else if (gps_data.valid) {
          last_gps_time_seconds = current_gps_time_seconds;
      }

      if (gps_result == HAL_OK) {
        osDelay(1000);
        printf("GPS data before sync: valid=%d, time=%02d:%02d:%02d %02d/%02d/%04d\r\n",
               gps_data.valid, gps_data.hour, gps_data.minute, gps_data.second,
               gps_data.day, gps_data.month, gps_data.year);
        if (gps_data.valid) {
            GPSModule_SyncRTC();
            printf("GPS data valid, waiting 1 second for ephemeris data save...\r\n");
            osDelay(1000);  // 等待GPS模块保存星历数据
        }

else {
            printf("GPS data invalid, keeping current RTC time\r\n");
        }

        RTC_GetDateTime(&current_wakeup_time, &current_wakeup_date);
        rtc_time_valid = 1;
        printf("RTC synced to GPS time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
               current_wakeup_time.Hours, current_wakeup_time.Minutes, current_wakeup_time.Seconds,
               current_wakeup_date.Date, current_wakeup_date.Month, current_wakeup_date.Year);
        uint8_t post_gps_work_check = NetworkCommand_CheckWorkTime();
        if (post_gps_work_check != 1) {
            printf("Post-GPS check: Not in work hours, skipping GSM and data processing\r\n");
            gsm_can_start = 0;
            gps_task_completed = 1;
            GPSModule_PowerOff();
            osSemaphoreRelease(gpsReadySemHandle);
            continue;
        }

      }

      if (is_first_boot) {
        is_first_boot = 0;
      }

      LED1_OFF;
      gps_task_completed = 1;
      printf("GPS task completed, powering off GPS module\r\n");
      GPSModule_PowerOff();  // 关闭GPS电源
      osSemaphoreRelease(gpsReadySemHandle);
    }

    osDelay(10);
  }

  /* USER CODE END StartGPSTask */
}

/* USER CODE BEGIN Header_StartAccelTask */
/**
* @brief 加速度传感器任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartAccelTask */
/* 加速度传感器任务主函数，负责IMU数据采集 */
void StartAccelTask(void const * argument)
{
  /* USER CODE BEGIN StartAccelTask */

 /* 无限循环 */
  for(;;)
  {
    if (osSemaphoreWait(sensorStartSemHandle, osWaitForever) == osOK) {
      osDelay(1000);
      SensorModule_Init();
      SensorModule_ReadData();
      osSemaphoreRelease(sensorReadySemHandle);
    }

    osDelay(10);
  }

  /* USER CODE END StartAccelTask */
}

/* USER CODE BEGIN Header_StartGSMTask */
/**
* @brief GSM任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartGSMTask */
/* GSM任务主函数，负责GSM模块初始化和通信 */
void StartGSMTask(void const * argument)
{
  /* USER CODE BEGIN StartGSMTask */

 /* 无限循环 */
  for(;;)
  {
    if (osSemaphoreWait(gsmStartSemHandle, osWaitForever) == osOK) {
      while (!gsm_can_start) {
        osDelay(100);
      }

      GSM_Status_t gsm_status = GSM_SimpleInit();
      osSemaphoreRelease(gsmReadySemHandle);
    }

    osDelay(10);
  }

  /* USER CODE END StartGSMTask */
}

/* USER CODE BEGIN Header_StartFlashTask */
/**
* @brief Flash任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartFlashTask */
/* Flash任务主函数，负责Flash存储管理 */
void StartFlashTask(void const * argument)
{
  /* USER CODE BEGIN StartFlashTask */

 /* 无限循环 */
  for(;;)
  {
    osDelay(1);
  }

  /* USER CODE END StartFlashTask */
}

/* USER CODE BEGIN Header_StartPowerTask */
/**
* @brief 主电源控制任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartPowerTask */
/* 主电源控制任务，负责系统主循环和电源管理 */
void StartPowerTask(void const * argument)
{
  /* USER CODE BEGIN StartPowerTask */
  unsigned long cycle_count = 0;
  printf("Main Task OK\r\n");
  NetworkCommand_LoadSettingsOnBoot();
  printf("Historical Data: %d\r\n", SPI_FLASH_GetRecordCount());

 /* 无限循环 */
  for(;;)
  {
    cycle_count++;
    printf("===== Cycle #%lu (Wakeup #%lu, Time: %lu min) =====\r\n",
           cycle_count, device_wakeup_count, GetCurrentWakeupTime());
    osSemaphoreWait(gpsStartSemHandle, 0);
    osSemaphoreWait(sensorStartSemHandle, 0);
    osSemaphoreWait(gsmStartSemHandle, 0);
    osSemaphoreWait(gpsReadySemHandle, 0);
    osSemaphoreWait(sensorReadySemHandle, 0);
    osSemaphoreWait(gsmReadySemHandle, 0);
    gps_task_completed = 0;
    gsm_can_start = 1;
    SystemModule_PrintCurrentTime();
    PowerModule_Init();
    PowerModule_ReadBatteryVoltage();
    #if ENABLE_VOLTAGE_MONITOR_TEST
    if (is_first_boot) {
        uint32_t monitor_start_time = HAL_GetTick();
        uint32_t last_voltage_print = monitor_start_time;
        uint32_t print_interval = 1000;
        while ((HAL_GetTick() - monitor_start_time) < (VOLTAGE_MONITOR_DURATION * 1000)) {
            if ((HAL_GetTick() - last_voltage_print) >= print_interval) {
                last_voltage_print = HAL_GetTick();
                PowerModule_ReadBatteryVoltage();
                printf("Battery: %.3f V\r\n", pw);
            }

            osDelay(50);
        }

        printf("电压监测测试完成\r\n");
        SystemModule_EnterSleepMode();
        continue;
    }

    #endif
    if (NetworkCommand_CheckLongSleepStatus()) {
        printf("Long sleep tracking: continuing sleep cycle\r\n");
        SystemModule_EnterSleepMode();
        continue;
    }

    uint8_t voltage_protection_result = BatteryProtection_CheckVoltage();
    if (voltage_protection_result == 1) {
        printf("Low voltage protection active - skipping data collection\r\n");
        SystemModule_EnterSleepMode();
        continue;
    }
    else if (voltage_protection_result == 2) {
        // 电压恢复正常，继续正常工作流程
    }
    else if (voltage_protection_result == 3) {
        printf("Low voltage mode active - continue data collection with extended sleep interval\r\n");
        // 低电压模式：继续正常的数据采集流程，但在休眠时使用延长的间隔
    }

    device_wakeup_count++;
    uint8_t pre_work_time_result = NetworkCommand_CheckWorkTime();
    if (pre_work_time_result == 2) {
    }

else if (pre_work_time_result == 0) {
        NetworkCommand_Result_t work_time_check;
        if (NetworkCommand_LoadFromFlash(CMD_TYPE_WORK_TIME, &work_time_check) == HAL_OK && work_time_check.is_valid) {
        }

else {
        }

    }

else if (pre_work_time_result == 3) {
    }

else {
    }

    if (is_first_boot && strcmp(gsm_ccid, "TEST0001") == 0) {
      printf("First boot - attempting to get CCID...\r\n");
      GSM_PowerOn();
      osDelay(3000);
      if (GSM_Init() == GSM_OK) {
        char temp_ccid[32];
        if (GSM_GetCCID(temp_ccid) == GSM_OK) {
          strcpy(gsm_ccid, temp_ccid);
          printf("CCID obtained: %s\r\n", gsm_ccid);
        }

else {
          printf("Failed to get CCID, using default\r\n");
        }

        uint8_t signal;
        if (GSM_GetSignal(&signal) == GSM_OK) {
          gsm_signal_quality = (int8_t)signal;
          printf("Signal quality: %d\r\n", gsm_signal_quality);
        }

      }

else {
        printf("GSM init failed, using default CCID\r\n");
      }

      GSM_PowerOff();
      osDelay(1000);
    }

    uint8_t skip_gsm_this_cycle = NetworkCommand_ShouldSkipGSM();
    if (skip_gsm_this_cycle) {
        printf("Skip GSM this cycle - N command active\r\n");
    }

    // 新的GPS启动策略：默认不启动GPS，只有初次启动或收到T3指令时才启动
    uint8_t should_start_gps = 0;
    if (is_first_boot) {
        should_start_gps = 1;
        gps_enable_mode = 1;  // 设置GPS启动模式
        printf("First boot - starting GPS for RTC sync\r\n");
    } else if (NetworkCommand_IsGPSOnCommand()) {
        should_start_gps = 1;
        gps_enable_mode = 1;  // 设置GPS启动模式
        printf("T3 command received - starting GPS\r\n");
        NetworkCommand_ClearGPSCommands(); // 清除T3指令标志
    } else {
        gps_enable_mode = 0;  // 设置为默认模式（不启动GPS）
        printf("Default mode - skipping GPS, will send zero GPS data\r\n");
    }

    uint8_t gps_ok = 0, gsm_ok = 0;
    if (should_start_gps) {
        osSemaphoreRelease(gpsStartSemHandle);
        uint32_t gps_timeout = is_first_boot ? (GPS_TIMEOUT_FIRST_BOOT + 10) * 1000 : (GPS_TIMEOUT_NORMAL + 10) * 1000;
        if (osSemaphoreWait(gpsReadySemHandle, gps_timeout) == osOK) {
          gps_ok = 1;
        }
    } else {
        // 不启动GPS时，直接设置GPS任务完成标志
        gps_task_completed = 1;
        gps_ok = 0; // 表示没有获取到GPS数据
    }

    osSemaphoreRelease(sensorStartSemHandle);

    osSemaphoreWait(sensorReadySemHandle, osWaitForever);
    if (gsm_can_start == 0) {
        SystemModule_EnterSleepMode();
        continue;
    }

    if (!skip_gsm_this_cycle) {
        printf("GPS task completed, starting GSM module...\r\n");
        osDelay(200);  // 等待GPS电源完全关闭后再启动GSM
        gsm_can_start = 1;
        osSemaphoreRelease(gsmStartSemHandle);
        if (osSemaphoreWait(gsmReadySemHandle, osWaitForever) == osOK) {
          gsm_ok = 1;
        }

    }

else {
        gsm_ok = 0;
    }

    if (gps_ok && gps_data.valid) {
    }

    char data_string_s[300];
    char data_string_b[300];
    if (DataModule_CreatePacket(data_string_s, sizeof(data_string_s)) == HAL_OK) {
      strcpy(data_string_b, data_string_s);
      char *s_pos = strchr(data_string_b, 'S');
      if (s_pos != NULL) {
        *s_pos = 'B';
      }

      uint8_t data_sent_successfully = 0;
      if (skip_gsm_this_cycle) {
        data_sent_successfully = 0;
      }

      else if (gsm_ok && GSM_GetState() == GSM_STATE_CONNECTED) {
        GSM_Status_t send_result = GSM_SendData(data_string_s, strlen(data_string_s));
        if (send_result == GSM_OK) {
          data_sent_successfully = 1;
          DataModule_PrintData(data_string_s);
          uint32_t historical_count = SPI_FLASH_GetRecordCount();
          if (historical_count > 0) {
            printf("Starting batch historical data sending, total records: %lu\r\n", historical_count);
            uint32_t sent_batches = 0;
            uint32_t max_batches = (HISTORICAL_DATA_SEND_LIMIT + HISTORICAL_DATA_BATCH_SIZE - 1) / HISTORICAL_DATA_BATCH_SIZE;
            while (sent_batches < max_batches && SPI_FLASH_GetRecordCount() > 0) {
              uint32_t batch_count = 0;
              if (BatchHistoricalData(g_batch_data_buffer, BATCH_BUFFER_SIZE, &batch_count) == HAL_OK) {
                printf("Sending batch %lu with %lu records, data length: %lu bytes\r\n",
                       sent_batches + 1, batch_count, strlen(g_batch_data_buffer));
                GSM_Status_t batch_send_result = GSM_SendData(g_batch_data_buffer, strlen(g_batch_data_buffer));
                if (batch_send_result == GSM_OK) {
                  printf("Batch %lu sent successfully (%lu records)\r\n",
                         sent_batches + 1, batch_count);
                  sent_batches++;
                }

else {
                  printf("Batch %lu send failed, need to restore %lu records\r\n",
                         sent_batches + 1, batch_count);
                  break;
                }

              }

else {
                break;
              }

            }

            printf("Historical Data: %d\r\n", SPI_FLASH_GetRecordCount());
          }

          if (gps_data.valid) {
            float current_speed = gps_data.speed;
            #if AB_COMMAND_TEST_ENABLE
            current_speed = VIRTUAL_SPEED_VALUE;
            printf("AB Test Mode: Using virtual speed %.1f knots (Real GPS: %.1f knots)\r\n",
                   current_speed, gps_data.speed);
            #endif
            NetworkCommand_CheckSpeedThreshold(current_speed);
          }

          // T3/T0指令处理逻辑
          if (NetworkCommand_IsGPSOnCommand()) {
            printf("T3 command detected - starting GPS immediately\r\n");
            NetworkCommand_ClearGPSCommands(); // 清除T3指令标志
            gps_enable_mode = 1;  // 设置GPS启动模式

            // 立即启动GPS获取定位
            osSemaphoreRelease(gpsStartSemHandle);
            uint32_t gps_timeout = (GPS_TIMEOUT_NORMAL + 10) * 1000;
            if (osSemaphoreWait(gpsReadySemHandle, gps_timeout) == osOK) {
              printf("T3 GPS positioning completed\r\n");

              // 如果获取到有效GPS数据，重新发送包含真实GPS数据的包
              if (gps_data.valid) {
                printf("T3 GPS data valid, sending updated data packet\r\n");
                char updated_data_string[300];
                if (DataModule_CreatePacket(updated_data_string, sizeof(updated_data_string)) == HAL_OK) {
                  GSM_Status_t t3_send_result = GSM_SendData(updated_data_string, strlen(updated_data_string));
                  if (t3_send_result == GSM_OK) {
                    printf("T3 updated data sent successfully\r\n");
                    DataModule_PrintData(updated_data_string);
                  } else {
                    printf("T3 updated data send failed\r\n");
                  }
                }
              }
            } else {
              printf("T3 GPS positioning timeout\r\n");
            }
          } else if (NetworkCommand_IsGPSOffCommand()) {
            printf("T0 command detected - will enter sleep mode\r\n");
            NetworkCommand_ClearGPSCommands(); // 清除T0指令标志
            // T0指令处理在后续的休眠逻辑中进行
          }

        }

else {
          printf("Real-time data send failed\r\n");
          data_sent_successfully = 0;
        }

      }

else {
        printf("GSM not connected, cannot send data\r\n");
        data_sent_successfully = 0;
      }

      if (gsm_ok && GSM_GetState() == GSM_STATE_CONNECTED) {
        GSM_CloseServer();
      }

      if (!data_sent_successfully) {
        uint8_t writeData[SPI_FLASH_RECORD_SIZE];
        memset(writeData, 0, SPI_FLASH_RECORD_SIZE);
        size_t data_len = strlen(data_string_b);
        if (data_len >= SPI_FLASH_RECORD_SIZE) {
        }

else {
          strcpy((char *)writeData, data_string_b);
          if (SPI_FLASH_WriteRecord(writeData, SPI_FLASH_RECORD_SIZE) == HAL_OK) {
            printf("Data cached to SPI Flash (send failed, Unread: %d)\r\n", SPI_FLASH_GetRecordCount());
          }

else {
            printf("SPI Flash write failed, data lost!\r\n");
          }

        }

      }

      if (!data_sent_successfully) {
        DataModule_PrintData(data_string_b);
      }

    }

else {
      printf("Data synthesis failed\r\n");
    }

    GPSModule_ClearData();

    // T0指令处理：如果收到T0指令，确保进入休眠
    if (NetworkCommand_IsGPSOffCommand()) {
        printf("T0 command active - entering sleep mode\r\n");
        NetworkCommand_ClearGPSCommands(); // 清除T0指令标志
    }

    SystemModule_EnterSleepMode();
    printf("Cycle #%lu completed\r\n\n", cycle_count);
  }

  /* USER CODE END StartPowerTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */
/* LED闪烁控制函数 */
void BlinkLEDs(uint8_t count, uint32_t delay)
{
  for (uint8_t i = 0; i < count; i++)
  {
    LED1_ON;
    osDelay(delay);
    LED1_OFF;
    osDelay(delay);
  }

  LED1_OFF;
}

/* 进入STOP低功耗模式 */
void EnterStopMode(void)
{
  LED1_OFF;
  GPS_PWR_OFF;
  V_OUT_OFF;
  RF_PWR_OFF;
  VCHK_OFF;
  HAL_UART_AbortReceive_IT(&huart1);
  HAL_UART_AbortReceive_IT(&hlpuart1);
  HAL_NVIC_DisableIRQ(USART1_IRQn);
  HAL_NVIC_DisableIRQ(LPUART1_IRQn);
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_USART1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();
  __HAL_RCC_USART4_CLK_DISABLE();
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);
  __disable_irq();
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();
  uint32_t default_wakeup_counter = SLEEP_DURATION_SECONDS - 1;
  uint32_t logical_wakeup_counter = (wakeup_counter > 0) ? wakeup_counter : default_wakeup_counter;
  uint32_t actual_logical_seconds;
  uint32_t physical_rtc_counter = RTC_ApplyClockCorrection(logical_wakeup_counter, &actual_logical_seconds);
  last_actual_logical_seconds = actual_logical_seconds;
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, physical_rtc_counter, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    __enable_irq();
    return;
  }

  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();
  __enable_irq();
  RTC_TimeTypeDef time_before;
  RTC_DateTypeDef date_before;
  HAL_RTC_GetTime(&hrtc, &time_before, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_before, RTC_FORMAT_BIN);
  printf("Time before sleep: %02d:%02d:%02d\r\n",
         time_before.Hours, time_before.Minutes, time_before.Seconds);
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);
  __disable_irq();
  SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICER[i] = 0xFFFFFFFF;
  }

  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICPR[i] = 0xFFFFFFFF;
  }

  vTaskSuspendAll();
  __enable_irq();
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
  LED1_ON;
  SystemClock_Config();
  __HAL_RCC_USART1_CLK_ENABLE();
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_USART4_CLK_ENABLE();
  __HAL_RCC_I2C1_CLK_ENABLE();
  __HAL_RCC_DMA1_CLK_ENABLE();
  MX_USART1_UART_Init();
  MX_LPUART1_UART_Init();
  MX_USART4_UART_Init();
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;
  __enable_irq();
  xTaskResumeAll();
  LED1_OFF;
  osDelay(100);
  LED1_ON;
  RTC_TimeTypeDef time_after;
  RTC_DateTypeDef date_after;
  HAL_RTC_GetTime(&hrtc, &time_after, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_after, RTC_FORMAT_BIN);
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);
  printf("Woke up from sleep mode\r\n");
  printf("Time after sleep: %02d:%02d:%02d\r\n",
         time_after.Hours, time_after.Minutes, time_after.Seconds);
  int elapsed_seconds = (time_after.Hours - time_before.Hours) * 3600 +
                        (time_after.Minutes - time_before.Minutes) * 60 +
                        (time_after.Seconds - time_before.Seconds);
  if (elapsed_seconds < 0) {
    elapsed_seconds += 24 * 3600;
  }

  printf("Elapsed time: %d seconds\r\n", elapsed_seconds);
  PowerPins_InitForWakeup();
  V_OUT_ON;
  osDelay(500);
  memset(gps_buffer, 0, GPS_BUFFER_SIZE);
  gps_buffer_index = 0;
  if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
      HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }

  if (hi2c1.State == HAL_I2C_STATE_RESET) {
      MX_I2C1_Init();
  }

}

/* RTC唤醒定时器中断回调函数 */
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
  rtcWakeupFlag = 1;
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();
}

/* 电源模块初始化 */
HAL_StatusTypeDef PowerModule_Init(void)
{
    V_OUT_ON;
    VCHK_ON;
    osDelay(500);
    return HAL_OK;
}

/* 读取电池电压 */
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[60];
    memset(ADC_Value, 0, sizeof(ADC_Value));
    VCHK_ON;
    osDelay(10);
    float voltage = 0.0f;
    HAL_ADC_DeInit(&hadc);
    osDelay(10);
    MX_ADC_Init();
    osDelay(10);
    HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED);
    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)ADC_Value, 60) == HAL_OK) {
        osDelay(100);
        uint32_t battery_adc_sum = 0;
        uint32_t temp_adc_sum = 0;
        uint32_t vrefint_adc_sum = 0;
        uint32_t battery_count = 0;
        uint32_t temp_count = 0;
        uint32_t vrefint_count = 0;
        for (int i = 0; i < 60; i += 3) {
            battery_adc_sum += ADC_Value[i];
            battery_count++;
            if (i + 1 < 60) {
                vrefint_adc_sum += ADC_Value[i + 1];
                vrefint_count++;
            }

            if (i + 2 < 60) {
                temp_adc_sum += ADC_Value[i + 2];
                temp_count++;
            }

        }

        float adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
        float vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;
        float temp_raw = (temp_count > 0) ? (temp_adc_sum / (float)temp_count) : 0;
        HAL_ADC_Stop_DMA(&hadc);
        if (temp_raw > 0 && vrefint_raw > 0) {
            uint16_t vrefint_cal = VREFINT_CAL_VALUE;
            float actual_vdd_temp = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;
            float temp_corrected = temp_raw * actual_vdd_temp / 3.0f;
            mcu_temp = Calculate_MCU_Temperature((uint32_t)temp_corrected);
        }

        if (vrefint_raw > 0) {
            uint16_t vrefint_cal = VREFINT_CAL_VALUE;
            float actual_vdd = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;
            voltage = (adc_raw * actual_vdd / 4096.0f) * 2.0f;
        }

else {
            voltage = (adc_raw * 3.3f / 4096.0f) * 2.0f;
        }

        voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;
    }

else {
        printf("ADC DMA startup failed!\r\n");
    }

    VCHK_OFF;
    pw = voltage;
    printf("Battery: %.3f V\r\n", pw);
    return HAL_OK;
}

/* 使能外设电源 */
void PowerModule_EnablePeripherals(void)
{
    GPS_PWR_ON;
    V_OUT_ON;
    VCHK_ON;
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;
    if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
        HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
    }

    LED1_ON;
    osDelay(50);
    LED1_OFF;
    if (hi2c1.State == HAL_I2C_STATE_RESET) {
        MX_I2C1_Init();
    }

}

/* 关闭外设电源 */
void PowerModule_DisablePeripherals(void)
{
    V_OUT_OFF;
    GPS_PWR_OFF;
    RF_PWR_OFF;
    VCHK_OFF;
}

/* GPS模块初始化 */
HAL_StatusTypeDef GPSModule_Init(void)
{
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;
    gps_new_data = 0;
    gps_data_ready = 0;
    printf("GPS module initialized\r\n");
    return HAL_OK;
}

/* GPS模块上电 */
void GPSModule_PowerOn(void)
{
    printf("GPS: Power ON\r\n");
    GPS_PWR_ON;
    osDelay(200);
}

/* GPS模块断电 */
void GPSModule_PowerOff(void)
{
    GPS_PWR_OFF;
}

/* 清除GPS数据 */
void GPSModule_ClearData(void)
{
    printf("GPS data before clear: valid=%d, time=%02d:%02d:%02d %02d/%02d/%04d\r\n",
           gps_data.valid, gps_data.hour, gps_data.minute, gps_data.second,
           gps_data.day, gps_data.month, gps_data.year);
    memset(&gps_data, 0, sizeof(GPS_Data_t));
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;
    gps_new_data = 0;
    gps_data_ready = 0;
    printf("GPS data cleared\r\n");
}

/* 等待GPS数据接收 */
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t unused_timeout, uint8_t is_first_boot)
{
    uint32_t gps_wait_start;
    uint8_t gps_data_valid = 0;
    uint8_t has_mode_6_data = 0;
    GPS_Data_t temp_mode_6_data;
    memset(&gps_data, 0, sizeof(GPS_Data_t));
    gps_data_ready = 0;
    gps_new_data = 0;
    gps_buffer_index = 0;
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    unsigned long  gps_wait_timeout = is_first_boot ? GPS_TIMEOUT_FIRST_BOOT * 1000 : GPS_TIMEOUT_NORMAL * 1000;
    printf("GPS wait: %lu seconds (%s boot)\r\n", gps_wait_timeout / 1000, is_first_boot ? "first" : "normal");
    gps_wait_start = HAL_GetTick();
    uint32_t last_led_toggle = HAL_GetTick();
    uint32_t last_debug_print = HAL_GetTick();
    uint8_t led_state = 1;
    LED1_ON;
    while ((HAL_GetTick() - gps_wait_start) < gps_wait_timeout) {
        if ((HAL_GetTick() - last_led_toggle) > 500) {
            led_state = !led_state;
            if (led_state) {
                LED1_ON;
            }

else {
                LED1_OFF;
            }

            last_led_toggle = HAL_GetTick();
        }

        if (gps_new_data) {
            gps_new_data = 0;
            // 在等待期间持续输出GPS原始数据
//            printf("GPS RAW: %s", gps_buffer);
            GPS_ParseData();
        }

        #if GPS_DEBUG_ENABLE
        printf("GPS check: valid=%d, quality=%d, satellites=%d, lat=%.6f, lon=%.6f\r\n",
               gps_data.valid, gps_data.fix_quality, gps_data.satellites,
               gps_data.latitude_decimal, gps_data.longitude_decimal);
        #endif
        if (gps_data.valid && gps_data.fix_quality == 1) {
            printf("GPS precise fix: quality=1, satellites=%d\r\n", gps_data.satellites);
            gps_data_valid = 1;
            break;
        }

        else if (gps_data.valid && gps_data.fix_quality == 6 && !has_mode_6_data) {
            printf("GPS estimated fix stored: quality=6, satellites=%d (waiting for quality 1)\r\n", gps_data.satellites);
            memcpy(&temp_mode_6_data, &gps_data, sizeof(GPS_Data_t));
            has_mode_6_data = 1;
        }

        osDelay(100);
    }

    LED1_OFF;
    if (gps_data_valid) {
        return HAL_OK;
    }

else if (has_mode_6_data) {
        printf("Using stored GPS quality 6 data (no quality 1 fix available)\r\n");
        memcpy(&gps_data, &temp_mode_6_data, sizeof(GPS_Data_t));
        return HAL_OK;
    }

else {
        printf("GPS timeout: no valid data received, using quality 0\r\n");
        gps_data.latitude = 0.0;
        gps_data.longitude = 0.0;
        gps_data.latitude_decimal = 0.0;
        gps_data.longitude_decimal = 0.0;
        gps_data.altitude = 0.0;
        gps_data.fix_quality = 0;
        gps_data.satellites = 0;
        gps_data.speed = 0.0;
        gps_data.course = 0.0;
        gps_data.hdop = 99.9;
        gps_data.pdop = 99.9;
        gps_data.vdop = 99.9;
        gps_data.valid = 0;
        return HAL_TIMEOUT;
    }

}

/* GPS时间同步到RTC */
HAL_StatusTypeDef GPSModule_SyncRTC(void)
{
    if (gps_data.valid && gps_data.hour <= 23 && gps_data.minute <= 59 && gps_data.second <= 59) {
        if (gps_data.year >= 2020 && gps_data.month >= 1 && gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31) {
            HAL_StatusTypeDef result = RTC_SetDateTime(
                gps_data.hour, gps_data.minute, gps_data.second,
                gps_data.day, gps_data.month, gps_data.year % 100
            );
            return result;
        }

else {
            return RTC_SetTime(gps_data.hour, gps_data.minute, gps_data.second);
        }

    }

    return HAL_ERROR;
}

/* 传感器模块初始化 */
HAL_StatusTypeDef SensorModule_Init(void)
{
    HAL_StatusTypeDef result = HAL_OK;
    __HAL_RCC_I2C1_CLK_ENABLE();
    MX_I2C1_Init();
    osDelay(50);
    return result;
}

/* 读取传感器数据 */
HAL_StatusTypeDef SensorModule_ReadData(void)
{
    if (HAL_I2C_IsDeviceReady(&hi2c1, LSM6DS3_ADDR, 3, 100) == HAL_OK) {
        LSM6DS3_Init(&hi2c1);
        if (!attitude.initialized) {
            LSM6DS3_InitAttitude(&attitude);
        }

        osDelay(20);
        LSM6DS3_ReadData(&hi2c1, &imuData);
        LSM6DS3_ComplementaryFilter(&imuData, &attitude);
        printf("Roll=%.1f, Pitch=%.1f\r\n", attitude.roll, attitude.pitch);
				printf("GPS signal waiting......\r\n");
        return HAL_OK;
    }

else {
        memset(&imuData, 0, sizeof(LSM6DS3_Data));
        imuData.temp_celsius = 25.0f;
        memset(&attitude, 0, sizeof(LSM6DS3_Attitude));
        return HAL_ERROR;
    }

}

/* 创建数据包 */
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size)
{
    return Create_Data_String(output_buffer, buffer_size);
}

/* 打印数据包内容 */
void DataModule_PrintData(const char *data_string)
{
    printf("TX Data output: %s\r\n", data_string);
}

/* 打印当前系统时间 */
void SystemModule_PrintCurrentTime(void)
{
    RTC_TimeTypeDef time;
    RTC_DateTypeDef date;
    RTC_GetDateTime(&time, &date);
    current_wakeup_time = time;
    current_wakeup_date = date;
    if (date.Date == 1 && date.Month == 1 && date.Year == 25) {
        rtc_time_valid = 0;
        printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d (DEFAULT - NOT SYNCED)\r\n",
               time.Hours, time.Minutes, time.Seconds,
               date.Date, date.Month, date.Year);
    }

else {
        rtc_time_valid = 1;
        printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
               time.Hours, time.Minutes, time.Seconds,
               date.Date, date.Month, date.Year);
    }

}

/* 系统进入休眠模式 */
void SystemModule_EnterSleepMode(void)
{
    LED1_OFF;
    if (GSM_GetState() == GSM_STATE_CONNECTED) {
        GSM_CloseServer();
        osDelay(500);
    }

    GSM_PowerOff();
    PowerModule_DisablePeripherals();
    RTC_TimeTypeDef time_before_sleep;
    RTC_DateTypeDef date_before_sleep;
    HAL_RTC_GetTime(&hrtc, &time_before_sleep, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date_before_sleep, RTC_FORMAT_BIN);
    printf("Time before sleep: %02d:%02d:%02d\r\n",
           time_before_sleep.Hours, time_before_sleep.Minutes, time_before_sleep.Seconds);
    NetworkCommand_CheckWorkTime();
    uint32_t sleep_seconds;
    extern uint32_t smart_sleep_seconds;
    extern uint8_t smart_sleep_active;
    extern uint32_t user_sleep_seconds;

    // 检查是否处于低电压模式（3.4V-3.6V之间）
    if (low_voltage_mode_active) {
        sleep_seconds = LOW_VOLTAGE_SLEEP_INTERVAL_SECONDS;
        printf("Using low voltage mode sleep time: %lu seconds (%.1f hours)\r\n",
               sleep_seconds, sleep_seconds / 3600.0f);
    }
    else if (smart_sleep_active && smart_sleep_seconds > 0) {
        sleep_seconds = smart_sleep_seconds;
        printf("Using smart sleep time: %lu seconds\r\n", sleep_seconds);
        smart_sleep_active = 0;
        smart_sleep_seconds = 0;
    }

else if (user_sleep_seconds > 0) {
        sleep_seconds = user_sleep_seconds;
        printf("Using user-set sleep time: %lu seconds\r\n", sleep_seconds);
    }

else {
        sleep_seconds = SLEEP_DURATION_SECONDS;
        NetworkCommand_Result_t sleep_result;
        if (NetworkCommand_LoadFromFlash(CMD_TYPE_SLEEP_DAYS, &sleep_result) == HAL_OK && sleep_result.is_valid) {
            switch (sleep_result.type) {
                case CMD_TYPE_SLEEP_DAYS:
                    sleep_seconds = sleep_result.value1 * 24 * 3600;
                    printf("Using sleep setting from FLASH: %lu days (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_HOURS:
                    sleep_seconds = sleep_result.value1 * 3600;
                    printf("Using sleep setting from FLASH: %lu hours (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_MINUTES:
                    sleep_seconds = sleep_result.value1 * 60;
                    printf("Using sleep setting from FLASH: %lu minutes (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_SECONDS:
                    sleep_seconds = sleep_result.value1;
                    printf("Using sleep setting from FLASH: %lu seconds\r\n", sleep_seconds);
                    break;
                default:
                    printf("Invalid sleep command type in FLASH, using default: %d seconds\r\n", SLEEP_DURATION_SECONDS);
                    break;
            }

            user_sleep_seconds = sleep_seconds;
        }

else {
            printf("No sleep setting in FLASH, using default: %d seconds\r\n", SLEEP_DURATION_SECONDS);
            user_sleep_seconds = SLEEP_DURATION_SECONDS;
        }

    }

    wakeup_counter = sleep_seconds - 1;
    printf("Sleep for %lu seconds\r\n", sleep_seconds);
    EnterStopMode();
    extern uint32_t last_actual_logical_seconds;
    NetworkCommand_UpdateLongSleepProgress(last_actual_logical_seconds);
    printf("=======================END==========================\r\n");
		printf("\r\n");
    printf("\r\n");
}

/* 检查电池电压保护状态 */
uint8_t BatteryProtection_CheckVoltage(void)
{
    float current_voltage = pw;

    // 检查是否低于保护阈值（3.4V）
    if (current_voltage < BATTERY_LOW_VOLTAGE_THRESHOLD) {
        if (!low_voltage_protection_active) {
            printf("Battery protection activated: %.3fV < %.1fV\r\n",
                   current_voltage, BATTERY_LOW_VOLTAGE_THRESHOLD);
            low_voltage_protection_active = 1;
        }
        // 清除低电压模式标志，因为已进入保护模式
        low_voltage_mode_active = 0;
        return 1;  // 保护模式：直接休眠
    }

    // 检查是否高于恢复阈值（3.6V）
    else if (current_voltage >= BATTERY_RECOVERY_VOLTAGE_THRESHOLD) {
        if (low_voltage_protection_active) {
            low_voltage_protection_active = 0;
            printf("Battery voltage recovered: %.3fV >= %.1fV\r\n",
                   current_voltage, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
        }
        // 清除低电压模式标志，电压已恢复正常
        if (low_voltage_mode_active) {
            low_voltage_mode_active = 0;
            printf("Low voltage mode deactivated: %.3fV >= %.1fV\r\n",
                   current_voltage, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
        }
        return 2;  // 电压恢复：正常工作
    }

    // 电压在3.4V-3.6V之间：低电压模式
    else {
        if (!low_voltage_mode_active) {
            low_voltage_mode_active = 1;
            printf("Low voltage mode activated: %.3fV (%.1fV - %.1fV)\r\n",
                   current_voltage, BATTERY_LOW_VOLTAGE_THRESHOLD, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
        }
        return 3;  // 低电压模式：延长休眠间隔
    }
}

/* 进入低电压保护模式 */
void BatteryProtection_EnterLowVoltageMode(void)
{
    printf("*** Low voltage protection activated ***\r\n");
    printf("Battery voltage: %.3fV < %.1fV (threshold)\r\n",
           pw, BATTERY_LOW_VOLTAGE_THRESHOLD);
    LED1_OFF;
    PowerModule_DisablePeripherals();
    V_OUT_OFF;
    GPS_PWR_OFF;
    printf("Device entering low power protection mode\r\n");
    vTaskDelay(pdMS_TO_TICKS(SLEEP_DURATION_SECONDS * 1000));
}

/* 退出低电压保护模式 */
void BatteryProtection_ExitLowVoltageMode(void)
{
    printf("*** Voltage recovery detected ***\r\n");
    printf("Battery voltage: %.3fV > %.1fV (recovery threshold)\r\n",
           pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
    PowerModule_EnablePeripherals();
    printf("Device resumed normal operation\r\n");
}

/**
  * @brief  计算STM32内部温度传感器的温度值
  * @param  temp_adc_value: 温度传感器的ADC原始值
  * @retval 计算得到的温度值（摄氏度）
  */
/* 计算STM32内部温度传感器的温度值 */
float Calculate_MCU_Temperature(uint32_t temp_adc_value)
{
    uint16_t *temp30_cal = (uint16_t*)0x1FF8007A;
    uint16_t *temp130_cal = (uint16_t*)0x1FF8007E;
    uint16_t cal_30 = *temp30_cal;
    uint16_t cal_130 = *temp130_cal;
    float temperature = 30.0f + ((float)temp_adc_value - (float)cal_30) * 100.0f / ((float)cal_130 - (float)cal_30);
    #include "GPS.h"
    temperature = temperature * TEMP_CALIBRATION_FACTOR + TEMP_OFFSET;
    return temperature;
}

/* 获取当前唤醒时间（分钟） */
uint32_t GetCurrentWakeupTime(void)
{
    extern uint32_t user_sleep_seconds;
    uint32_t sleep_seconds = (user_sleep_seconds > 0) ? user_sleep_seconds : SLEEP_DURATION_SECONDS;
    return device_wakeup_count * sleep_seconds / 60;
}

/* USER CODE END Application */

