# STM32 GPS+GSM 数据采集与传输系统

## 项目概述

这是一个基于STM32L071KBU6微控制器的低功耗数据采集与传输系统，主要用于GPS定位数据和传感器数据的采集、处理和远程传输。系统采用FreeRTOS实时操作系统，支持GPS定位、GSM网络通信、传感器数据采集和智能电源管理。

### 核心特性
- **超低功耗设计**：STOP模式休眠，功耗μA级别
- **智能工作时间段**：支持设置工作时间段，非工作时间自动长时间休眠
- **RTC时钟精确**：使用LSE外部晶振，提供精确的休眠时间
- **可靠数据传输**：失败数据自动存储，网络恢复后补发
- **远程指令控制**：支持服务器下发指令动态调整设备参数

## 主要功能

### 1. GPS定位系统
- **高精度定位**：支持GPS模块数据解析，获取经纬度、海拔、卫星数量等信息
- **时钟同步**：自动使用GPS时间同步RTC时钟
- **智能等待**：首次启动等待5分钟，正常工作等待2分钟 使用两个时间宏定义
#define GPS_TIMEOUT_NORMAL 120        // GPS常规定位等待时间
#define GPS_TIMEOUT_FIRST_BOOT 300    // GPS初次启动等待时间
- **故障恢复**：连续3次失败后自动重启GPS模块

### 2. GSM网络通信
- **TCP连接**：自动连接到指定服务器(************:48085)
- **数据传输**：实时发送GPS和传感器数据到服务器
- **指令接收**：支持接收服务器下发的ZL+系列控制指令
- **历史数据**：发送失败的数据自动存储，成功连接后补发

### 3. 传感器数据采集
- **姿态传感器**：LSM6DS3六轴传感器，获取Roll/Pitch角度
- **电池监测**：实时监测电池电压
- **温度监测**：MCU内部温度传感器

### 4. 智能电源管理
- **低功耗休眠**：支持STOP模式，功耗极低
- **动态休眠时间**：支持网络指令动态调整休眠时间
- **工作时间控制**：支持设置工作时间段，非工作时间智能休眠 （当次收到设定时直接修改休眠时间到工作时间内）
- **电源隔离**：各模块独立电源控制，避免相互干扰

### 5. 数据存储系统
- **SPI Flash存储**：使用W25Q64芯片存储历史数据
- **环形缓冲区**：自动覆盖最旧数据，确保存储空间高效利用
- **数据完整性**：支持数据校验和恢复

## 系统架构

### 硬件架构
```
STM32L071KBU6 (主控)
├── GPS模块 (UART1)
├── GSM模块 (LPUART1)
├── LSM6DS3传感器 (I2C1)
├── W25Q64 Flash (SPI1)
├── RTC时钟
└── 电源管理电路
```

### 软件架构
```
FreeRTOS
├── GPS任务 (StartGPSTask)
├── GSM任务 (StartGSMTask)
├── 传感器任务 (StartAccelTask)
├── Flash任务 (StartFlashTask)
└── 主控任务 (StartPowerTask)
```

## 代码结构

### 核心文件
- **`Src/freertos.c`** - FreeRTOS任务管理和主控逻辑，系统核心调度
- **`Src/main.c`** - 系统初始化、中断处理、硬件配置
- **`Src/GSM.c`** - GSM模块AT指令库，网络通信核心
- **`Src/GPS.c`** - GPS数据解析和处理，定位功能实现
- **`Src/network_command.c`** - 网络指令解析和处理，ZL+指令系统
- **`Src/system_modules.c`** - 系统模块管理，电源控制

### 数据处理模块
- **`Src/rtc_sync.c`** - RTC时钟同步，GPS时间校准
- **`Src/rtc_config.c`** - RTC时钟修正配置，LSI偏差补偿
- **`Src/data_synthesis.c`** - 数据合成和格式化，数据包生成
- **`Src/lsm6ds3.c`** - 三轴传感器驱动，姿态数据采集

### 存储系统
- **`Src/SPI_FLASH/bsp_spi_flash.c`** - SPI Flash驱动，环形缓冲区管理
- **`Src/FLASH/bsp_flash.c`** - 内部Flash操作，配置参数存储

### 配置文件
- **`Inc/globals.h`** - 全局变量和系统配置
- **`Inc/rtc_config.h`** - RTC修正配置，时钟参数定义
- **`Inc/GSM.h`** - GSM模块接口定义
- **`Inc/GPS.h`** - GPS模块接口定义

## 详细工作流程

### 1. 系统启动流程
```
系统上电
├── 硬件初始化 (时钟、GPIO、UART、I2C、SPI)
├── FreeRTOS任务创建
├── 电源管理初始化
├── 从Flash加载配置参数
├── 显示历史数据数量
└── 进入主循环
```

### 2. 主循环工作流程
```
设备唤醒
├── 1. 打印当前RTC时间
├── 2. 电源管理初始化
├── 3. 电池电压检测
├── 4. 长时间休眠跟踪检查
│   ├── 需要继续休眠 → 直接进入休眠
│   └── 休眠完成 → 继续执行
├── 5. 工作时间段预检查
│   ├── 不在工作时间段 → 智能休眠
│   └── 在工作时间段 → 继续执行
├── 6. GPS启动策略判断（新增T3/T0模式）
│   ├── 初次启动 → 开启GPS同步RTC
│   ├── 收到T3指令 → 开启GPS定位
│   └── 默认模式 → 跳过GPS，发送全0 GPS数据
├── 7. 传感器数据采集任务
├── 8. GPS同步后工作时间段最终检查
│   ├── 不在工作时间段 → 跳过GSM，直接休眠
│   └── 在工作时间段 → 继续GSM工作
├── 9. GSM网络通信任务
├── 10. 数据合成和传输（支持全0 GPS数据）
├── 11. T3/T0指令处理
│   ├── 收到T3 → 立即开启GPS，获取真实定位，重新发送数据
│   └── 收到T0 → 标记进入休眠
├── 12. 历史数据补发
└── 13. 进入休眠模式
```

### 3. GPS数据采集详细流程
```
GPS任务启动
├── 开启GPS电源 (V_GPS_ON)
├── 等待GPS模块稳定
├── 开始数据接收
├── 等待定位成功
│   ├── 首次启动：等待5分钟
│   └── 正常启动：等待2分钟
├── GPS数据解析和验证
├── RTC时间同步 (如果GPS数据有效)
├── GPS同步后工作时间段检查
│   ├── 不在工作时间段 → 跳过后续处理
│   └── 在工作时间段 → 继续执行
├── 关闭GPS电源 (V_GPS_OFF)
└── 设置GPS任务完成标志
```

### 4. GSM网络通信详细流程
```
GSM任务启动
├── 开启GSM电源 (V_GSM_ON)
├── GSM模块初始化
│   ├── ATE0 (关闭回显)
│   ├── AT+CCID (获取SIM卡号)
│   └── AT+CSQ (获取信号强度)
├── 建立TCP连接
│   ├── AT+CIPSTART="TCP","************",48085
│   └── AT+CIPQSEND=1
├── 发送数据
│   ├── AT+CIPSEND=数据长度
│   ├── 等待">"提示符
│   ├── 发送实际数据
│   └── 等待DATA ACCEPT确认
├── 等待服务器ZL+指令响应
│   ├── 收到ZL+指令 → 解析并执行
│   └── 超时无响应 → 标记发送失败
├── 关闭连接 (AT+CIPCLOSE)
├── 关闭GSM电源 (V_GSM_OFF)
└── 设置GSM任务完成标志
```

### 5. 数据传输和存储流程
```
数据处理
├── 合成实时数据包 (S数据)
├── 合成存储数据包 (B数据)
├── GSM发送S数据
├── 发送结果判断
│   ├── 成功 (收到ZL+指令)
│   │   ├── 解析ZL+指令
│   │   ├── 更新设备配置
│   │   └── 继续下一步
│   └── 失败 (超时或错误)
│       ├── 保存B数据到SPI Flash
│       └── 增加历史数据计数
├── 历史数据补发
│   ├── 检查是否有历史数据
│   ├── 批量读取历史数据 (最多8条)
│   ├── 逐条发送历史数据
│   └── 发送成功后删除已发送数据
└── 数据处理完成
```

### 6. 休眠管理详细流程
```
休眠准备
├── 关闭所有外设电源
│   ├── V_GPS_OFF (GPS电源)
│   ├── V_GSM_OFF (GSM电源)
│   ├── V_OUT_OFF (传感器电源)
│   └── VCHK_OFF (ADC采样开关)
├── 计算休眠时间
│   ├── 工作时间段内 → 使用设定的休眠间隔
│   └── 工作时间段外 → 计算到下次工作时间的间隔
├── RTC时钟设置
│   ├── 逻辑时间 = 物理RTC时间 (LSE精确1:1)
│   ├── 超过65535秒 → 启用长时间休眠跟踪
│   └── 设置RTC唤醒定时器
├── 进入STOP模式
├── RTC定时唤醒
└── 唤醒后重新初始化外设
```

## 网络指令系统

### ZL+指令格式
系统支持接收服务器下发的ZL+系列指令，指令格式为：`ZL+参数1+参数2+...`

### 指令参数说明
| 参数 | 含义 | 格式 | 示例 | 说明 |
|------|------|------|------|------|
| **D** | 天 | D数值 | D1 | 设备休眠1天 |
| **H** | 小时 | H数值 | H12 | 设备休眠12小时 |
| **M** | 分钟 | M数值 | M30 | 设备休眠30分钟 |
| **S** | 秒 | S数值 | S600 | 设备休眠600秒 |
| **F...E** | 工作时间段 | F开始E结束 | F08E18 | 8点到18点工作 |
| **N** | 批量发送 | N数值 | N5 | 累积5条数据后发送 |
| **A** | 速度阈值 | A数值 | A10 | 速度超过10km/h触发快发 |
| **B** | 快发休眠时间 | B数值 | B300 | 快发模式下休眠300秒 |
| **T3** | GPS开启 | T3 | T3 | 立即开启GPS定位 |
| **T0** | GPS关闭 | T0 | T0 | 进入休眠模式 |

### 指令示例
```
ZL+S30                    // 设备每30秒工作一次
ZL+S30+F08E18            // 30秒间隔，8点到18点工作
ZL+S30+F08E18+N5         // 30秒间隔，8-18点工作，累积5条数据发送
ZL+S30+A10+B300          // 30秒间隔，速度>10km/h时300秒间隔
ZL+M10+F00E00            // 10分钟间隔，禁用工作时间段(全天工作)
ZL+H2+F23E01             // 2小时间隔，23点到1点工作
ZL+S30+T3                // 30秒间隔，立即开启GPS定位
ZL+S30+T0                // 30秒间隔，进入休眠模式
```

### 工作时间段特殊说明
- **F00E00**: 开始时间等于结束时间，表示禁用工作时间段功能，设备全天正常工作
- **F23E01**: 跨天工作时间段，从23点工作到次日1点
- **工作时间段外**: 设备自动计算到下次工作时间的休眠时间，进入长时间休眠

### 指令执行时机
- **立即生效**: 收到指令后立即更新设备配置
- **下次生效**: 休眠时间和工作时间段在当前周期结束后生效
- **持久保存**: 所有配置参数保存到Flash，断电重启后仍然有效

### T3/T0指令特殊说明（新增功能）
#### 工作模式变更
- **默认模式**: 设备唤醒后不启动GPS，发送全0的GPS数据（经纬度0，时间使用RTC，其他GPS参数为0）
- **T3模式**: 收到T3指令后立即开启GPS定位，获取真实GPS数据并重新发送
- **T0模式**: 收到T0指令后进入休眠模式

#### T3指令执行流程
1. 设备发送全0 GPS数据到服务器
2. 服务器返回包含T3的ZL指令
3. 设备立即开启GPS模块进行定位
4. 获取到有效GPS数据后，重新打包发送真实GPS数据
5. 完成后等待下次唤醒

#### T0指令执行流程
1. 设备发送全0 GPS数据到服务器
2. 服务器返回包含T0的ZL指令
3. 设备进入休眠模式
4. 下次唤醒后继续发送全0 GPS数据的循环

#### 数据格式变更
- **全0 GPS模式**: 经度=0.0, 纬度=0.0, 海拔=0.0, GPS状态=0, 卫星数=0, 速度=0.0, HDOP=0.0, PDOP=0.0, 方位角=0.0
- **时间来源**: 全0 GPS模式下使用RTC时间而非GPS时间
- **非GPS数据**: 电池电压、温度、三轴传感器数据保持实时采集

## 数据格式

设备发送的数据格式为字符串，格式如下：
格式: HY[长度][S]+[经度]+[纬度]+[UTC时间]+[海拔]+[GPS定位状态]+[卫星数量]+[速度]+[电池电压]+[温度]+[HDOP]+[PDOP]+[陀螺仪X]+[陀螺仪Y]+[陀螺仪Z]+[方位角]+[GSM信号]+[ICCID]+E

字段说明:
HY：云端识别标识
[数据长度]：从S开始到E的数据字节数量
S: 当前数据头部标识
经度: 格式如"11957.64581"
纬度: 格式如"3016.51036"
UTC时间: 格式如"065026.220524" (时分秒.年月日)
海拔: 单位米，如"30.5"
GPS定位状态: 单字符，'1'=精确定位，'6'=估算定位
速度: 单位公里，如"8.5"
电池电压: 如"3.85"表示3.85V
温度: 整数值  “26”
HDOP: 水平精度因子，如"2.0"
PDOP: 位置精度因子，如"3.0"
陀螺仪XYZ: 三轴角度值，整数
GPS方位角: 如"74.85"
信号: 输出原始值 不做强度判断
ICCID: 设备号
E: 固定分隔符

示例：
HY114S+11957.72266+3016.56006+011014.230725+34.4+1+8+0.0+3.85+29.4+2.9+3.0+-1.6+-0.6+0.1+0.00+31+89430103223249458271+E

## 编译和部署

### 开发环境
- **IDE**: Keil MDK-ARM
- **芯片**: STM32L071KBU6
- **RTOS**: FreeRTOS
- **HAL库**: STM32L0xx HAL Driver

### 项目配置
```
项目根目录: GSM迁徙版/
├── Core/                    // 核心代码目录
│   ├── Src/                // 源文件
│   └── Inc/                // 头文件
├── MDK-ARM/                // Keil项目文件
│   └── Camera_CAT1.uvprojx // Keil工程文件
└── Camera_CAT1.ioc         // CubeMX配置文件
```

### 编译步骤
1. 打开Keil MDK-ARM
2. 打开项目文件: `MDK-ARM/Camera_CAT1.uvprojx`
3. 配置编译器 (ARM Compiler 5/6)
4. 编译生成.axf/.hex文件
5. 使用ST-Link下载到目标板

### 关键编译配置
- **优化等级**: -O1 (平衡代码大小和性能)
- **浮点运算**: 软件浮点 (STM32L071无硬件FPU)
- **堆栈大小**: 根据FreeRTOS任务需求配置

## 关键技术要点

### 1. RTC时钟配置机制
```c
// 配置文件: Inc/rtc_config.h
#define RTC_CLOCK_CORRECTION_FACTOR 1.0f   // LSE时钟无需修正
#define RTC_MAX_LOGICAL_SLEEP_SECONDS 65535U // 最大逻辑休眠时间

// 时钟函数: Src/freertos.c
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds)
```
**原理**: STM32使用LSE外部32.768KHz晶振时钟源，精度高无需修正。逻辑时间与物理时间1:1对应，最大休眠时间为65535秒。

**LSE时钟优势**:
- 精度高：±20ppm典型精度，远优于LSI的±17%偏差
- 温度稳定：受温度影响小，长期运行稳定
- 无需校正：消除了复杂的时钟偏差补偿机制
- 跨天准确：0点附近时间跳转准确，无日期错乱问题

### 2. 跨天时间判断机制（已简化）
```c
// 工作时间检查 (Src/freertos.c) - LSE时钟精确，简化处理
uint8_t IsWithinWorkTime(void) {
    // 处理跨天工作时间，如22:00-06:00 - LSE时钟精确无需特殊处理
    if (start_minutes <= end_minutes) {
        // 不跨天：09:00-17:00
        within_work_time = (current >= start && current < end);
    } else {
        // 跨天：22:00-06:00
        within_work_time = (current >= start || current < end);
    }
}
```
**原理**: 使用LSE精确时钟，将时间转换为分钟数进行比较，简化跨天工作时间的处理逻辑。

### 2.1 LSE时钟简化改进
```c
// 移除的复杂机制 (原LSI时钟补偿)
- detect_040625_error()           // 特定日期错误检测
- validate_date_continuity()      // 复杂的日期连续性验证
- save_valid_date_to_backup()     // 日期备份机制
- restore_date_from_backup()      // 日期恢复机制
- 午夜跨天特殊处理逻辑              // 0点附近日期跳转判断

// 简化后的机制 (LSE精确时钟)
- is_date_reasonable()            // 基本日期有效性检查
- 直接使用GPS时间同步RTC         // 无需复杂验证
- 移除时钟偏差修正因子            // 1:1时间映射
```
**改进效果**:
- 消除0点附近日期错乱问题
- 减少代码复杂度约70%
- 提高时间同步可靠性
- 简化调试和维护

### 3. 长时间休眠跟踪机制
```c
// 核心变量
uint32_t target_sleep_seconds;   // 目标休眠总时间
uint32_t elapsed_sleep_seconds;  // 已休眠时间
uint8_t long_sleep_active;       // 长时间休眠激活标志

// 关键函数
NetworkCommand_InitLongSleepTracking()    // 初始化长时间休眠
NetworkCommand_CheckLongSleepStatus()     // 检查休眠状态
NetworkCommand_UpdateLongSleepProgress()  // 更新休眠进度
```
**原理**: RTC最大计数65535秒(约18小时)，超长时间通过分段休眠实现。设备记录目标时间和已休眠时间，每次唤醒检查是否需要继续休眠。

### 4. 工作时间段智能休眠
```c
// 时间计算函数
uint32_t NetworkCommand_CalculateSecondsToWorkTime(uint8_t start_hour, uint8_t end_hour,
                                                   uint8_t current_hour, uint8_t current_minute, uint8_t current_second)

// 工作时间段检查
uint8_t NetworkCommand_CheckWorkTime()
```
**原理**: 设备在工作时间段外自动计算到下次工作时间的间隔，进入长时间休眠。支持跨天时间段(如23:00-01:00)。

### 4. 双重工作时间段检查机制
```c
// GPS同步前预检查
uint8_t pre_work_time_result = NetworkCommand_CheckWorkTime();

// GPS同步后最终检查
uint8_t final_work_time_result = NetworkCommand_CheckWorkTime();
```
**原理**: GPS同步可能更新RTC时间，导致工作时间段判断变化。通过GPS同步前后两次检查，确保只在真正的工作时间段内启动GSM和发送数据。

## 开发和调试指南

### 1. 电源管理要点
- **电源控制引脚**:
  ```c
  V_GPS_ON/OFF    // GPS模块电源控制
  V_GSM_ON/OFF    // GSM模块电源控制
  V_OUT_ON/OFF    // 传感器电源控制
  VCHK_ON/OFF     // ADC采样开关控制
  ```
- **时序要求**: GPS和GSM模块不能同时工作，避免UART中断冲突
- **休眠前**: 必须关闭所有外设电源，确保低功耗

### 2. 任务同步机制
```c
// 关键同步标志位
uint8_t gps_task_completed = 0;  // GPS任务完成标志
uint8_t gsm_can_start = 0;       // GSM可以启动标志

// 信号量同步
osSemaphoreWait(gpsReadySemHandle, osWaitForever);   // 等待GPS完成
osSemaphoreWait(gsmReadySemHandle, osWaitForever);   // 等待GSM完成
```

### 3. 数据传输可靠性
- **成功标准**: 收到服务器`ZL+`指令响应
- **失败处理**: 自动保存到SPI Flash环形缓冲区
- **补发机制**: 网络恢复后自动批量补发历史数据

### 4. 调试技巧
```c
// 关键调试输出
printf("RTC LSE: logical=%lu -> physical=%lu seconds (1:1 mapping)\r\n", logical_time, physical_time);
printf("Work time configured: %02d:00 - %02d:00, current: %02d:%02d:%02d\r\n", start, end, h, m, s);
printf("Long sleep tracking: %lu/%lu seconds, remaining: %lu seconds\r\n", elapsed, target, remaining);
```

### 5. 常见问题排查
| 问题现象 | 可能原因 | 排查方法 |
|---------|---------|---------|
| 设备提前唤醒 | RTC修正未生效 | 检查修正系数和计算逻辑 |
| 工作时间段外启动 | 双重检查失效 | 查看GPS同步前后时间变化 |
| 数据发送失败 | ZL+指令超时 | 检查网络连接和服务器响应 |
| 历史数据丢失 | Flash写入失败 | 检查SPI Flash状态和容量 |
| 功耗异常 | 外设未关闭 | 监控电源控制引脚状态 |

## 快速上手指南

### 1. 环境搭建
```bash
# 1. 安装Keil MDK-ARM (推荐5.37或更高版本)
# 2. 安装STM32L0xx器件支持包
# 3. 配置ST-Link驱动
# 4. 打开项目: MDK-ARM/Camera_CAT1.uvprojx
```

### 2. 首次运行
```c
// 1. 编译并下载程序
// 2. 连接串口调试 (波特率115200)
// 3. 观察启动日志:
//    - "Main Task OK" - 系统启动成功
//    - "Historical Data: X" - 历史数据数量
//    - GPS定位和GSM连接状态

// 4. 发送测试指令 (通过服务器):
//    ZL+S30+F00E00  // 30秒间隔，全天工作
```

### 3. 功能测试
| 测试项目 | 测试方法 | 预期结果 |
|---------|---------|---------|
| GPS定位 | 观察串口输出 | "GPS precise fix: quality=1, satellites=X" |
| GSM连接 | 检查服务器日志 | 收到设备数据包 |
| 工作时间段 | 发送F指令 | 设备在指定时间段工作 |
| 休眠功能 | 监控电流消耗 | 休眠时电流<100μA |
| 历史数据 | 断网后重连 | 自动补发历史数据 |

### 4. 常用修改点
```c
// 修改服务器地址 (Src/GSM.c)
#define SERVER_IP "************"
#define SERVER_PORT 48085

// 修改GPS等待时间 (Inc/GPS.h)
#define GPS_TIMEOUT_NORMAL 120        // 正常等待2分钟
#define GPS_TIMEOUT_FIRST_BOOT 300    // 首次等待5分钟

// RTC时钟配置 (Inc/rtc_config.h)
#define RTC_CLOCK_CORRECTION_FACTOR 1.0f   // LSE时钟精确无需修正

// 修改历史数据批量大小 (Src/freertos.c)
#define HISTORICAL_DATA_BATCH_SIZE 8  // 每次最多发送8条
```

## 版本历史

- **v1.0** - 基础GPS+GSM功能实现
- **v1.1** - 添加FreeRTOS多任务支持
- **v1.2** - 完善电源管理和时序控制
- **v1.3** - 修复ACCEPT判断逻辑，优化数据传输可靠性
- **v1.4** - 添加RTC时钟修正机制，提高休眠时间精度
- **v1.5** - 实现双重工作时间段检查，确保GPS同步后准确判断
- **v1.6** - 优化长时间休眠跟踪机制，支持超长时间休眠
- **v1.7** - 新增T3/T0指令支持，实现智能GPS启动策略和全0 GPS数据模式

## 技术支持

### 文档资源
- **项目说明.md** - 本文档，项目整体介绍
- **长时间休眠跟踪机制说明.md** - 长时间休眠详细说明
- **GSM_AT指令说明.md** - GSM通信协议文档

### 联系方式
如有技术问题或改进建议，请联系项目维护团队。

---
*最后更新: 2025-09-25*
